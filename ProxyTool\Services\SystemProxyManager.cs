using Microsoft.Win32;
using System;
using System.Runtime.InteropServices;

namespace ProxyTool.Services
{
    public class SystemProxyManager
    {
        private const string INTERNET_SETTINGS_KEY = @"Software\Microsoft\Windows\CurrentVersion\Internet Settings";
        
        [DllImport("wininet.dll")]
        private static extern bool InternetSetOption(IntPtr hInternet, int dwOption, IntPtr lpBuffer, int dwBufferLength);
        
        private const int INTERNET_OPTION_SETTINGS_CHANGED = 39;
        private const int INTERNET_OPTION_REFRESH = 37;

        private string? _originalProxyServer;
        private int _originalProxyEnable;
        private bool _proxyWasEnabled = false;

        /// <summary>
        /// 启用系统代理
        /// </summary>
        public bool EnableSystemProxy(string proxyServer, int proxyPort)
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(INTERNET_SETTINGS_KEY, true);
                if (key == null)
                {
                    throw new Exception("无法访问Internet设置注册表项");
                }

                // 保存原始设置
                _originalProxyServer = key.GetValue("ProxyServer")?.ToString();
                _originalProxyEnable = (int)(key.GetValue("ProxyEnable") ?? 0);
                _proxyWasEnabled = true;

                // 设置新的代理
                var proxyAddress = $"{proxyServer}:{proxyPort}";
                key.SetValue("ProxyServer", proxyAddress, RegistryValueKind.String);
                key.SetValue("ProxyEnable", 1, RegistryValueKind.DWord);

                // 刷新Internet设置
                RefreshInternetSettings();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"启用系统代理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 禁用系统代理
        /// </summary>
        public bool DisableSystemProxy()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(INTERNET_SETTINGS_KEY, true);
                if (key == null)
                {
                    throw new Exception("无法访问Internet设置注册表项");
                }

                if (_proxyWasEnabled)
                {
                    // 恢复原始设置
                    if (!string.IsNullOrEmpty(_originalProxyServer))
                    {
                        key.SetValue("ProxyServer", _originalProxyServer, RegistryValueKind.String);
                    }
                    else
                    {
                        key.DeleteValue("ProxyServer", false);
                    }
                    
                    key.SetValue("ProxyEnable", _originalProxyEnable, RegistryValueKind.DWord);
                }
                else
                {
                    // 直接禁用代理
                    key.SetValue("ProxyEnable", 0, RegistryValueKind.DWord);
                }

                // 刷新Internet设置
                RefreshInternetSettings();

                _proxyWasEnabled = false;
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"禁用系统代理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查系统代理是否已启用
        /// </summary>
        public bool IsSystemProxyEnabled()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(INTERNET_SETTINGS_KEY, false);
                if (key == null) return false;

                var proxyEnable = (int)(key.GetValue("ProxyEnable") ?? 0);
                return proxyEnable == 1;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取当前系统代理设置
        /// </summary>
        public (bool enabled, string? server) GetCurrentProxySettings()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(INTERNET_SETTINGS_KEY, false);
                if (key == null) return (false, null);

                var proxyEnable = (int)(key.GetValue("ProxyEnable") ?? 0);
                var proxyServer = key.GetValue("ProxyServer")?.ToString();

                return (proxyEnable == 1, proxyServer);
            }
            catch
            {
                return (false, null);
            }
        }

        /// <summary>
        /// 刷新Internet设置
        /// </summary>
        private void RefreshInternetSettings()
        {
            InternetSetOption(IntPtr.Zero, INTERNET_OPTION_SETTINGS_CHANGED, IntPtr.Zero, 0);
            InternetSetOption(IntPtr.Zero, INTERNET_OPTION_REFRESH, IntPtr.Zero, 0);
        }

        /// <summary>
        /// 设置代理绕过列表
        /// </summary>
        public void SetProxyBypass(string bypassList = "localhost;127.*;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*")
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(INTERNET_SETTINGS_KEY, true);
                if (key == null) return;

                key.SetValue("ProxyOverride", bypassList, RegistryValueKind.String);
                RefreshInternetSettings();
            }
            catch
            {
                // 忽略设置绕过列表的错误
            }
        }
    }
}
