<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>SSGI Proxy Tool</AssemblyTitle>
    <AssemblyDescription>Shadowsocks代理管理工具</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2025</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="7.0.2" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="..\conf\**" />
    <EmbeddedResource Include="..\ssgi-1.3.0.jar" />
    <EmbeddedResource Include="..\lib\**" />
  </ItemGroup>

  <ItemGroup>
    <None Include="app.manifest" />
  </ItemGroup>

</Project>
