using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProxyTool.Models;
using ProxyTool.Services;

namespace ProxyTool
{
    public partial class MainForm : Form
    {
        private readonly ProcessManager _processManager;
        private readonly SystemProxyManager _systemProxyManager;
        private readonly TunManager _tunManager;
        private ProxyConfig? _config;
        private int _currentPort;

        private Button _btnToggleProxy;
        private CheckBox _chkSystemProxy;
        private CheckBox _chkTunMode;
        private Label _lblStatus;
        private TextBox _txtLog;
        private NotifyIcon _notifyIcon;

        public MainForm()
        {
            _processManager = new ProcessManager();
            _systemProxyManager = new SystemProxyManager();
            _tunManager = new TunManager();

            InitializeComponent();
            InitializeEvents();
            LoadConfiguration();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            Text = "SSGI Proxy Tool";
            Size = new Size(500, 400);
            StartPosition = FormStartPosition.CenterScreen;
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            ShowInTaskbar = true;

            // 主要控件
            _btnToggleProxy = new Button
            {
                Text = "启动代理",
                Size = new Size(120, 40),
                Location = new Point(20, 20),
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _chkSystemProxy = new CheckBox
            {
                Text = "启用系统代理",
                Location = new Point(160, 30),
                Size = new Size(120, 20),
                Font = new Font("Microsoft YaHei", 9F),
                Enabled = false
            };

            _chkTunMode = new CheckBox
            {
                Text = "启用TUN模式",
                Location = new Point(300, 30),
                Size = new Size(120, 20),
                Font = new Font("Microsoft YaHei", 9F),
                Enabled = false
            };

            _lblStatus = new Label
            {
                Text = "状态: 未启动",
                Location = new Point(20, 80),
                Size = new Size(450, 20),
                Font = new Font("Microsoft YaHei", 9F),
                ForeColor = Color.Gray
            };

            _txtLog = new TextBox
            {
                Location = new Point(20, 110),
                Size = new Size(450, 220),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 8F),
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            // 系统托盘图标
            _notifyIcon = new NotifyIcon
            {
                Text = "SSGI Proxy Tool",
                Visible = true
            };

            // 添加控件到窗体
            Controls.AddRange(new Control[] {
                _btnToggleProxy,
                _chkSystemProxy,
                _chkTunMode,
                _lblStatus,
                _txtLog
            });

            // 创建托盘菜单
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("显示主窗口", null, (s, e) => { Show(); WindowState = FormWindowState.Normal; });
            contextMenu.Items.Add("-");
            contextMenu.Items.Add("退出", null, (s, e) => { Application.Exit(); });
            _notifyIcon.ContextMenuStrip = contextMenu;
        }

        private void InitializeEvents()
        {
            _btnToggleProxy.Click += BtnToggleProxy_Click;
            _chkSystemProxy.CheckedChanged += ChkSystemProxy_CheckedChanged;
            _chkTunMode.CheckedChanged += ChkTunMode_CheckedChanged;
            
            _notifyIcon.DoubleClick += (s, e) => { Show(); WindowState = FormWindowState.Normal; };
            
            Resize += (s, e) => { if (WindowState == FormWindowState.Minimized) Hide(); };
            
            FormClosing += (s, e) => {
                if (e.CloseReason == CloseReason.UserClosing)
                {
                    e.Cancel = true;
                    Hide();
                }
            };

            // 进程管理器事件
            _processManager.OutputReceived += (s, msg) => AppendLog($"[INFO] {msg}");
            _processManager.ErrorReceived += (s, msg) => AppendLog($"[ERROR] {msg}");
            _processManager.ProcessExited += (s, e) => {
                Invoke(() => {
                    UpdateProxyStatus(false);
                    AppendLog("[WARN] 代理进程意外退出");
                });
            };
        }

        private async void LoadConfiguration()
        {
            try
            {
                _config = ProxyConfig.LoadFromEmbeddedResource();
                _currentPort = _config.GetActualPort();
                AppendLog($"[INFO] 配置加载成功，端口: {_currentPort}");
            }
            catch (Exception ex)
            {
                AppendLog($"[ERROR] 加载配置失败: {ex.Message}");
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnToggleProxy_Click(object? sender, EventArgs e)
        {
            if (_config == null)
            {
                MessageBox.Show("配置未加载，无法启动代理", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            _btnToggleProxy.Enabled = false;

            try
            {
                if (_processManager.IsRunning)
                {
                    // 停止代理
                    await StopProxyAsync();
                }
                else
                {
                    // 启动代理
                    await StartProxyAsync();
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[ERROR] {ex.Message}");
                MessageBox.Show(ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _btnToggleProxy.Enabled = true;
            }
        }

        private async Task StartProxyAsync()
        {
            AppendLog("[INFO] 正在启动代理...");
            
            if (await _processManager.StartProxyAsync(_config!))
            {
                UpdateProxyStatus(true);
                AppendLog($"[INFO] 代理启动成功，监听端口: {_currentPort}");
            }
            else
            {
                AppendLog("[ERROR] 代理启动失败");
                throw new Exception("代理启动失败");
            }
        }

        private async Task StopProxyAsync()
        {
            AppendLog("[INFO] 正在停止代理...");
            
            // 先禁用系统代理和TUN模式
            if (_chkSystemProxy.Checked)
            {
                _chkSystemProxy.Checked = false;
            }
            
            if (_chkTunMode.Checked)
            {
                _chkTunMode.Checked = false;
            }
            
            _processManager.StopProxy();
            UpdateProxyStatus(false);
            AppendLog("[INFO] 代理已停止");
        }

        private async void ChkSystemProxy_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                if (_chkSystemProxy.Checked)
                {
                    _systemProxyManager.EnableSystemProxy("127.0.0.1", _currentPort);
                    _systemProxyManager.SetProxyBypass();
                    AppendLog("[INFO] 系统代理已启用");
                }
                else
                {
                    _systemProxyManager.DisableSystemProxy();
                    AppendLog("[INFO] 系统代理已禁用");
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[ERROR] 系统代理设置失败: {ex.Message}");
                _chkSystemProxy.Checked = !_chkSystemProxy.Checked; // 恢复状态
            }
        }

        private async void ChkTunMode_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                if (_chkTunMode.Checked)
                {
                    if (await _tunManager.EnableTunModeAsync("127.0.0.1", _currentPort))
                    {
                        AppendLog("[INFO] TUN模式已启用");
                    }
                    else
                    {
                        throw new Exception("TUN模式启用失败");
                    }
                }
                else
                {
                    if (await _tunManager.DisableTunModeAsync())
                    {
                        AppendLog("[INFO] TUN模式已禁用");
                    }
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[ERROR] TUN模式设置失败: {ex.Message}");
                _chkTunMode.Checked = !_chkTunMode.Checked; // 恢复状态
            }
        }

        private void UpdateProxyStatus(bool isRunning)
        {
            if (InvokeRequired)
            {
                Invoke(() => UpdateProxyStatus(isRunning));
                return;
            }

            _btnToggleProxy.Text = isRunning ? "停止代理" : "启动代理";
            _btnToggleProxy.BackColor = isRunning ? Color.FromArgb(220, 53, 69) : Color.FromArgb(0, 122, 204);
            _lblStatus.Text = $"状态: {(isRunning ? $"运行中 (端口: {_currentPort})" : "未启动")}";
            _lblStatus.ForeColor = isRunning ? Color.Green : Color.Gray;
            
            _chkSystemProxy.Enabled = isRunning;
            _chkTunMode.Enabled = isRunning;
            
            if (!isRunning)
            {
                _chkSystemProxy.Checked = false;
                _chkTunMode.Checked = false;
            }

            _notifyIcon.Text = $"SSGI Proxy Tool - {(isRunning ? "运行中" : "已停止")}";
        }

        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(() => AppendLog(message));
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            _txtLog.AppendText($"[{timestamp}] {message}\r\n");
            _txtLog.SelectionStart = _txtLog.Text.Length;
            _txtLog.ScrollToCaret();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _processManager?.Dispose();
                _notifyIcon?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
