using System;
using System.Diagnostics;
using System.IO;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace ProxyTool.Services
{
    public class TunManager
    {
        private const string TUN_ADAPTER_NAME = "SSGITun";
        private bool _tunEnabled = false;
        private string? _originalGateway;
        private string? _originalDns;

        [DllImport("iphlpapi.dll", SetLastError = true)]
        private static extern int GetAdaptersInfo(IntPtr pAdapterInfo, ref int pBufOutLen);

        /// <summary>
        /// 启用TUN模式
        /// </summary>
        public async Task<bool> EnableTunModeAsync(string proxyServer, int proxyPort)
        {
            try
            {
                if (_tunEnabled)
                {
                    return true;
                }

                // 保存原始网络设置
                await SaveOriginalNetworkSettingsAsync();

                // 创建TUN适配器
                if (!await CreateTunAdapterAsync())
                {
                    throw new Exception("创建TUN适配器失败");
                }

                // 配置路由表
                if (!await ConfigureRoutingAsync(proxyServer, proxyPort))
                {
                    throw new Exception("配置路由表失败");
                }

                _tunEnabled = true;
                return true;
            }
            catch (Exception ex)
            {
                await DisableTunModeAsync();
                throw new Exception($"启用TUN模式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 禁用TUN模式
        /// </summary>
        public async Task<bool> DisableTunModeAsync()
        {
            try
            {
                if (!_tunEnabled)
                {
                    return true;
                }

                // 恢复原始路由
                await RestoreOriginalRoutingAsync();

                // 删除TUN适配器
                await RemoveTunAdapterAsync();

                _tunEnabled = false;
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"禁用TUN模式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查TUN模式是否已启用
        /// </summary>
        public bool IsTunModeEnabled()
        {
            return _tunEnabled;
        }

        /// <summary>
        /// 保存原始网络设置
        /// </summary>
        private async Task SaveOriginalNetworkSettingsAsync()
        {
            try
            {
                // 获取默认网关
                var result = await RunCommandAsync("route", "print 0.0.0.0");
                // 解析默认网关信息
                _originalGateway = ParseDefaultGateway(result);

                // 获取DNS设置
                var dnsResult = await RunCommandAsync("netsh", "interface ip show dns");
                _originalDns = ParseDnsServers(dnsResult);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存原始网络设置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建TUN适配器
        /// </summary>
        private async Task<bool> CreateTunAdapterAsync()
        {
            try
            {
                // 检查是否已存在TUN适配器
                if (await CheckTunAdapterExistsAsync())
                {
                    return true;
                }

                // 使用netsh创建虚拟网络适配器
                var result = await RunCommandAsync("netsh", $"interface ip set interface \"{TUN_ADAPTER_NAME}\" admin=enable");
                
                // 如果适配器不存在，尝试创建
                if (result.Contains("找不到") || result.Contains("not found"))
                {
                    // 创建回环适配器作为TUN替代
                    await RunCommandAsync("netsh", "interface ip set interface \"Loopback\" admin=enable");
                    return true;
                }

                return !result.Contains("错误") && !result.Contains("error");
            }
            catch (Exception ex)
            {
                throw new Exception($"创建TUN适配器失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 配置路由表
        /// </summary>
        private async Task<bool> ConfigureRoutingAsync(string proxyServer, int proxyPort)
        {
            try
            {
                // 添加代理服务器的直连路由
                if (!string.IsNullOrEmpty(_originalGateway))
                {
                    await RunCommandAsync("route", $"add {proxyServer} mask *************** {_originalGateway} metric 1");
                }

                // 添加默认路由到TUN适配器
                await RunCommandAsync("route", "add 0.0.0.0 mask ********* 127.0.0.1 metric 1");
                await RunCommandAsync("route", "add ********* mask ********* 127.0.0.1 metric 1");

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"配置路由失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 恢复原始路由
        /// </summary>
        private async Task RestoreOriginalRoutingAsync()
        {
            try
            {
                // 删除添加的路由
                await RunCommandAsync("route", "delete 0.0.0.0 mask *********");
                await RunCommandAsync("route", "delete ********* mask *********");
            }
            catch
            {
                // 忽略删除路由的错误
            }
        }

        /// <summary>
        /// 删除TUN适配器
        /// </summary>
        private async Task RemoveTunAdapterAsync()
        {
            try
            {
                await RunCommandAsync("netsh", $"interface ip set interface \"{TUN_ADAPTER_NAME}\" admin=disable");
            }
            catch
            {
                // 忽略删除适配器的错误
            }
        }

        /// <summary>
        /// 检查TUN适配器是否存在
        /// </summary>
        private async Task<bool> CheckTunAdapterExistsAsync()
        {
            try
            {
                var result = await RunCommandAsync("netsh", "interface show interface");
                return result.Contains(TUN_ADAPTER_NAME);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 运行命令行命令
        /// </summary>
        private async Task<string> RunCommandAsync(string fileName, string arguments)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    StandardOutputEncoding = System.Text.Encoding.GetEncoding("GBK"),
                    StandardErrorEncoding = System.Text.Encoding.GetEncoding("GBK")
                };

                using var process = new Process { StartInfo = startInfo };
                process.Start();

                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                return string.IsNullOrEmpty(error) ? output : $"{output}\n{error}";
            }
            catch (Exception ex)
            {
                throw new Exception($"执行命令失败 {fileName} {arguments}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析默认网关
        /// </summary>
        private string ParseDefaultGateway(string routeOutput)
        {
            try
            {
                var lines = routeOutput.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("0.0.0.0") && line.Contains("0.0.0.0"))
                    {
                        var parts = line.Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 3)
                        {
                            return parts[2]; // 网关地址
                        }
                    }
                }
                return "192.168.1.1"; // 默认网关
            }
            catch
            {
                return "192.168.1.1";
            }
        }

        /// <summary>
        /// 解析DNS服务器
        /// </summary>
        private string ParseDnsServers(string dnsOutput)
        {
            try
            {
                // 简单解析，实际实现可能需要更复杂的逻辑
                return "8.8.8.8,8.8.4.4";
            }
            catch
            {
                return "8.8.8.8,8.8.4.4";
            }
        }
    }
}
