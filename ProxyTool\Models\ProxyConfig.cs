using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace ProxyTool.Models
{
    public class ProxyConfig
    {
        [JsonProperty("server")]
        public string Server { get; set; } = "127.0.0.1";

        [JsonProperty("port_password")]
        public Dictionary<string, string> PortPassword { get; set; } = new Dictionary<string, string>();

        [JsonProperty("method")]
        public string Method { get; set; } = "aes-256-cfb";

        /// <summary>
        /// 获取实际使用的端口号
        /// </summary>
        public int GetActualPort()
        {
            if (PortPassword.Count == 0)
                return GenerateRandomPort();

            var firstPort = PortPassword.Keys.First();
            if (firstPort == "0")
            {
                return GenerateRandomPort();
            }

            if (int.TryParse(firstPort, out int port))
            {
                return port;
            }

            return GenerateRandomPort();
        }

        /// <summary>
        /// 获取密码
        /// </summary>
        public string GetPassword()
        {
            if (PortPassword.Count == 0)
                return "default";

            return PortPassword.Values.First();
        }

        /// <summary>
        /// 生成10000以上的随机端口
        /// </summary>
        private int GenerateRandomPort()
        {
            var random = new Random();
            return random.Next(10000, 65535);
        }

        /// <summary>
        /// 从嵌入资源加载配置
        /// </summary>
        public static ProxyConfig LoadFromEmbeddedResource()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceName = assembly.GetManifestResourceNames()
                    .FirstOrDefault(name => name.EndsWith("config.json"));

                if (string.IsNullOrEmpty(resourceName))
                {
                    throw new FileNotFoundException("未找到嵌入的config.json文件");
                }

                using var stream = assembly.GetManifestResourceStream(resourceName);
                using var reader = new StreamReader(stream);
                var json = reader.ReadToEnd();

                return JsonConvert.DeserializeObject<ProxyConfig>(json) ?? new ProxyConfig();
            }
            catch (Exception ex)
            {
                throw new Exception($"加载配置文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存配置到临时文件
        /// </summary>
        public string SaveToTempFile(int actualPort)
        {
            try
            {
                var tempDir = Path.Combine(Path.GetTempPath(), "SSGIProxy");
                Directory.CreateDirectory(tempDir);

                var configPath = Path.Combine(tempDir, "config.json");

                // 创建临时配置，使用实际端口
                var tempConfig = new ProxyConfig
                {
                    Server = this.Server,
                    Method = this.Method,
                    PortPassword = new Dictionary<string, string>
                    {
                        { actualPort.ToString(), GetPassword() }
                    }
                };

                var json = JsonConvert.SerializeObject(tempConfig, Formatting.Indented);
                File.WriteAllText(configPath, json);

                return configPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"保存临时配置文件失败: {ex.Message}", ex);
            }
        }
    }
}
