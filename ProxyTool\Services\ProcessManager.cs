using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using ProxyTool.Models;

namespace ProxyTool.Services
{
    public class ProcessManager : IDisposable
    {
        private Process? _javaProcess;
        private string? _tempDirectory;
        private bool _disposed = false;

        public bool IsRunning => _javaProcess != null && !_javaProcess.HasExited;

        public event EventHandler<string>? OutputReceived;
        public event EventHandler<string>? ErrorReceived;
        public event EventHandler? ProcessExited;

        /// <summary>
        /// 启动Java代理进程
        /// </summary>
        public async Task<bool> StartProxyAsync(ProxyConfig config)
        {
            try
            {
                if (IsRunning)
                {
                    return true;
                }

                // 创建临时目录
                _tempDirectory = Path.Combine(Path.GetTempPath(), "SSGIProxy", Guid.NewGuid().ToString());
                Directory.CreateDirectory(_tempDirectory);

                // 提取嵌入的资源文件
                await ExtractEmbeddedResourcesAsync();

                // 获取实际端口并保存配置
                var actualPort = config.GetActualPort();
                var configPath = config.SaveToTempFile(actualPort);

                // 复制配置文件到临时目录
                var tempConfigPath = Path.Combine(_tempDirectory, "conf", "config.json");
                Directory.CreateDirectory(Path.GetDirectoryName(tempConfigPath)!);
                File.Copy(configPath, tempConfigPath, true);

                // 启动Java进程
                var jarPath = Path.Combine(_tempDirectory, "ssgi-1.3.0.jar");
                var startInfo = new ProcessStartInfo
                {
                    FileName = "java",
                    Arguments = $"-jar \"{jarPath}\"",
                    WorkingDirectory = _tempDirectory,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                _javaProcess = new Process { StartInfo = startInfo };
                _javaProcess.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        OutputReceived?.Invoke(this, e.Data);
                    }
                };

                _javaProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        ErrorReceived?.Invoke(this, e.Data);
                    }
                };

                _javaProcess.Exited += (sender, e) =>
                {
                    ProcessExited?.Invoke(this, EventArgs.Empty);
                };

                _javaProcess.EnableRaisingEvents = true;

                if (_javaProcess.Start())
                {
                    _javaProcess.BeginOutputReadLine();
                    _javaProcess.BeginErrorReadLine();
                    
                    // 等待一段时间确保进程正常启动
                    await Task.Delay(2000);
                    
                    return !_javaProcess.HasExited;
                }

                return false;
            }
            catch (Exception ex)
            {
                ErrorReceived?.Invoke(this, $"启动代理失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止Java代理进程
        /// </summary>
        public void StopProxy()
        {
            try
            {
                if (_javaProcess != null && !_javaProcess.HasExited)
                {
                    _javaProcess.Kill();
                    _javaProcess.WaitForExit(5000);
                }
            }
            catch (Exception ex)
            {
                ErrorReceived?.Invoke(this, $"停止代理失败: {ex.Message}");
            }
            finally
            {
                _javaProcess?.Dispose();
                _javaProcess = null;
                CleanupTempDirectory();
            }
        }

        /// <summary>
        /// 提取嵌入的资源文件
        /// </summary>
        private async Task ExtractEmbeddedResourcesAsync()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();

            foreach (var resourceName in resourceNames)
            {
                if (resourceName.Contains("ssgi-1.3.0.jar") || 
                    resourceName.Contains(".jar") || 
                    resourceName.Contains("config.json"))
                {
                    await ExtractResourceAsync(assembly, resourceName);
                }
            }
        }

        /// <summary>
        /// 提取单个资源文件
        /// </summary>
        private async Task ExtractResourceAsync(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null) return;

                // 确定目标路径
                var relativePath = GetRelativePathFromResourceName(resourceName);
                var targetPath = Path.Combine(_tempDirectory!, relativePath);

                // 创建目录
                var directory = Path.GetDirectoryName(targetPath);
                if (!string.IsNullOrEmpty(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 写入文件
                using var fileStream = File.Create(targetPath);
                await stream.CopyToAsync(fileStream);
            }
            catch (Exception ex)
            {
                ErrorReceived?.Invoke(this, $"提取资源文件失败 {resourceName}: {ex.Message}");
            }
        }

        /// <summary>
        /// 从资源名称获取相对路径
        /// </summary>
        private string GetRelativePathFromResourceName(string resourceName)
        {
            // 移除程序集前缀
            var parts = resourceName.Split('.');
            if (parts.Length >= 2)
            {
                // 跳过程序集名称部分
                var pathParts = parts.Skip(1).ToArray();
                var fileName = string.Join(".", pathParts);
                
                if (fileName.Contains("lib."))
                {
                    return Path.Combine("lib", fileName.Replace("lib.", ""));
                }
                else if (fileName.Contains("conf."))
                {
                    return Path.Combine("conf", fileName.Replace("conf.", ""));
                }
                else
                {
                    return fileName;
                }
            }
            
            return Path.GetFileName(resourceName);
        }

        /// <summary>
        /// 清理临时目录
        /// </summary>
        private void CleanupTempDirectory()
        {
            try
            {
                if (!string.IsNullOrEmpty(_tempDirectory) && Directory.Exists(_tempDirectory))
                {
                    Directory.Delete(_tempDirectory, true);
                }
            }
            catch
            {
                // 忽略清理错误
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopProxy();
                _disposed = true;
            }
        }
    }
}
